规则类型：比例尺相关的符号显示规则
规则目的：确保符号在不同比例尺下的合适显示

比例尺分级：
1. 大比例尺（1:10,000以下）
   - 符号显示策略：详细显示，包含所有细节
   - 最小符号大小：12像素
   - 最大符号密度：每平方厘米50个符号
   - 标注显示：显示详细标注信息
   - 符号样式：使用复杂的详细符号

2. 中比例尺（1:10,000 - 1:100,000）
   - 符号显示策略：选择性显示，突出重要要素
   - 最小符号大小：8像素
   - 最大符号密度：每平方厘米30个符号
   - 标注显示：显示重要标注信息
   - 符号样式：使用简化的标准符号

3. 小比例尺（1:100,000以上）
   - 符号显示策略：概括显示，仅显示重要要素
   - 最小符号大小：6像素
   - 最大符号密度：每平方厘米20个符号
   - 标注显示：仅显示关键标注
   - 符号样式：使用高度简化的符号

动态调整策略：
- 符号大小调整：size = base_size × scale_factor
- 符号密度控制：当密度超过阈值时进行聚合显示
- 重要性过滤：根据重要性等级过滤显示的要素
- 样式简化：根据比例尺自动选择合适的符号样式

特殊情况处理：
- 关键要素保护：重要要素在任何比例尺下都保持可见
- 用户自定义：允许用户覆盖默认的比例尺规则
- 性能优化：在性能受限时自动降低显示精度
- 交互响应：支持用户交互式调整显示参数