规则类型：概念澄清标准化
规则目的：将用户的模糊表达转换为系统可理解的精确概念

澄清维度：
1. 地理概念澄清
   - 模糊地区 → 具体国家列表
   - 相对位置 → 绝对坐标范围
   - 行政概念 → 标准行政区划
   - 功能概念 → 地理边界定义

2. 要素概念澄清
   - 通用名称 → 标准分类代码
   - 模糊描述 → 具体属性特征
   - 功能描述 → 数据表字段映射
   - 等级描述 → 量化评估标准

3. 时间概念澄清
   - 相对时间 → 绝对时间范围
   - 模糊时间 → 具体时间点
   - 周期性时间 → 时间序列定义
   - 实时概念 → 更新频率要求

4. 关系概念澄清
   - 空间关系 → 几何关系定义
   - 逻辑关系 → 属性关联规则
   - 时序关系 → 时间先后顺序
   - 因果关系 → 影响机制描述

澄清策略：
- 直接映射：有明确对应关系的概念
- 推理映射：需要逻辑推理的概念
- 多选映射：可能对应多个概念的情况
- 交互澄清：需要用户进一步确认的概念