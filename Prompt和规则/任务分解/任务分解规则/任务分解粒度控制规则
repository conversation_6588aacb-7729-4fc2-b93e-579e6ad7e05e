规则类型：任务粒度控制
规则目的：确保任务分解的粒度既不过粗也不过细

粒度判断标准：
1. 执行时间标准
   - 最小粒度：单个子任务执行时间不少于30秒
   - 最大粒度：单个子任务执行时间不超过10分钟
   - 理想粒度：单个子任务执行时间在1-5分钟之间

2. 复杂度标准
   - 简单任务：涉及单一数据源和单一处理逻辑
   - 中等任务：涉及多个数据源或复杂处理逻辑
   - 复杂任务：涉及多数据源整合和复杂分析逻辑

3. 依赖关系标准
   - 独立任务：不依赖其他任务的输出
   - 顺序任务：依赖前序任务的输出
   - 并行任务：可以与其他任务同时执行
   - 条件任务：基于条件判断是否执行

4. 资源需求标准
   - 轻量任务：主要消耗CPU资源，内存需求小
   - 重量任务：需要大量内存或存储资源
   - IO密集任务：主要进行数据读写操作
   - 计算密集任务：需要大量CPU计算资源

调整策略：
- 过粗分解：进一步细分为更小的子任务
- 过细分解：合并相关的小任务为较大任务
- 依赖过多：重新组织任务结构减少依赖
- 资源冲突：调整任务执行顺序避免资源竞争