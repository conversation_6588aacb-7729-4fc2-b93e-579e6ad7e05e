基于任务分解框架，对具体标绘任务进行详细分解：

输入信息：
- 用户需求：{user_requirement}
- 意图分析结果：{intent_analysis}
- 可用资源：{available_resources}
- 复杂度评估：{complexity_assessment}

分解步骤：

第一步：概念澄清任务生成
基于意图分析结果，生成概念澄清子任务：
- 地理概念澄清：将模糊地理概念转换为具体实体
- 要素概念澄清：将用户表达的要素映射到标准类型
- 时间概念澄清：明确时间范围和时效要求
- 关系概念澄清：识别实体间的关联关系

第二步：实体映射任务生成
基于澄清后的概念，生成实体映射子任务：
- 地理实体映射：概念到具体地理边界的映射
- 数据实体映射：要素类型到数据表的映射
- 符号实体映射：要素特征到符号类型的映射
- 规则实体映射：场景需求到制图规则的映射

第三步：数据操作任务生成
基于实体映射结果，生成数据操作子任务：
- 数据发现任务：在数据集索引中找到相关数据集
- 数据定位任务：在表索引中找到具体数据表
- 数据提取任务：从数据表中提取符合条件的记录
- 数据验证任务：检查数据的完整性和准确性

第四步：渲染合成任务生成
基于数据操作结果，生成渲染合成子任务：
- 符号匹配任务：为数据对象选择合适的符号
- 样式配置任务：根据制图规则配置样式参数
- 地图合成任务：将各要素合成到地图上
- 质量优化任务：优化地图的视觉效果和布局

第五步：依赖关系分析
分析子任务间的依赖关系：
- 顺序依赖：必须按顺序执行的任务
- 并行机会：可以并行执行的任务组
- 条件依赖：基于条件判断的执行路径
- 资源依赖：共享资源的任务协调

第六步：执行计划生成
生成优化的执行计划：
- 执行阶段划分：将任务分组到不同执行阶段
- 并行优化：最大化并行执行的效率
- 资源分配：合理分配计算和存储资源
- 错误处理：定义异常情况的处理策略

输出要求：
提供详细的任务分解结果，包含每个子任务的具体描述、输入输出、执行方法和质量标准。