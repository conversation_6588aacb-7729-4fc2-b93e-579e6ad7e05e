任务：解析要素概念"{feature_concept}"

解析维度：
1. 要素分类：
   - 基础设施类：港口/机场/车站/电站
   - 自然要素类：山峰/河流/湖泊
   - 社会要素类：城市/学校/医院
   - 事件要素类：地震/台风/冲突

2. 属性特征：
   - 几何类型：点/线/面
   - 重要性等级：国际级/国家级/地区级/地方级
   - 功能分类：商用/军用/民用/混合

3. 数据源映射：
   - 主要数据表：直接包含该要素的数据表
   - 关联数据表：包含相关信息的数据表
   - 补充数据源：可能需要的额外数据源

输出格式：
{
  "feature_category": "基础设施",
  "feature_subcategory": "交通设施",
  "geometry_type": "point",
  "importance_levels": ["国际级", "国家级"],
  "data_source_mapping": {
    "primary_tables": ["airport_data", "port_data"],
    "related_tables": ["country_info", "transportation_network"],
    "required_fields": ["name", "location", "type", "country"]
  }
}