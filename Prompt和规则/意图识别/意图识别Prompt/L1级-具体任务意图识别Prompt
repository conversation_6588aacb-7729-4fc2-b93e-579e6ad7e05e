基于元分析框架，针对具体任务进行深度意图分析：

当前任务：{user_input}
任务上下文：{task_context}
可用资源：{available_resources}

具体分析步骤：

步骤1：实体提取与分类
- 地理实体：提取所有地理相关的实体（国家、地区、城市等）
- 要素实体：提取要标绘的具体要素（港口、机场、事件等）
- 时间实体：提取时间相关的表达（时间点、时间段、频率等）
- 关系实体：提取实体间的关系（包含、邻近、因果等）

步骤2：意图分类与优先级
- 主要意图：识别用户的核心需求
- 次要意图：识别隐含的附加需求
- 意图优先级：确定各意图的重要程度
- 冲突解决：处理可能存在的意图冲突

步骤3：约束条件识别
- 显式约束：用户明确提出的限制条件
- 隐式约束：基于常识和经验推断的约束
- 技术约束：系统能力和资源的限制
- 业务约束：行业标准和规范的要求

步骤4：上下文补全
- 缺失信息推理：推断用户未明确表达的信息
- 默认参数设定：为未指定的参数设定合理默认值
- 场景适配：根据使用场景调整理解结果
- 用户偏好：基于历史数据推断用户偏好

输出要求：
提供详细的结构化分析结果，为任务分解提供充分的信息基础。