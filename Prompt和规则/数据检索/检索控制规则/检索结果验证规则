规则类型：检索结果质量验证
规则目的：确保检索结果满足任务需求

验证维度：
1. 完整性验证
   - 必需字段完整性：关键字段的非空比例 ≥ 95%
   - 记录数量合理性：结果数量在预期范围内
   - 覆盖范围完整性：地理覆盖范围符合需求
   - 时间覆盖完整性：时间范围覆盖符合需求

2. 准确性验证
   - 数据类型准确性：字段数据类型符合预期
   - 数值范围合理性：数值字段在合理范围内
   - 地理坐标有效性：坐标值在有效范围内
   - 时间格式正确性：时间字段格式标准化

3. 一致性验证
   - 内部一致性：同一记录内字段间的逻辑一致性
   - 外部一致性：与其他数据源的一致性
   - 格式一致性：数据格式的统一性
   - 编码一致性：字符编码的一致性

4. 时效性验证
   - 数据新鲜度：数据更新时间的新鲜程度
   - 时效符合度：数据时效与需求的符合程度
   - 更新频率：数据更新频率的合理性
   - 版本一致性：数据版本的一致性

验证流程：
1. 自动验证：系统自动执行基础验证规则
2. 抽样验证：对大数据集进行抽样质量检查
3. 交叉验证：与其他数据源进行交叉验证
4. 人工验证：对关键结果进行人工审核

处理策略：
- 通过验证：直接使用检索结果
- 部分通过：标记问题并提供修复建议
- 未通过验证：寻找备选数据源或降级处理
- 严重问题：终止任务并报告错误