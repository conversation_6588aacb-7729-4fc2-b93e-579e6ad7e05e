规则类型：检索匹配精度控制
规则目的：确保检索结果的高精度和高召回率

匹配维度权重配置：
1. 地理维度匹配（权重：30%）
   - 精确匹配：地理范围完全重叠 → 权重 1.0
   - 包含匹配：目标范围包含在数据范围内 → 权重 0.9
   - 相交匹配：地理范围部分重叠 → 权重 0.7
   - 邻近匹配：地理范围相邻但不重叠 → 权重 0.5

2. 语义维度匹配（权重：40%）
   - 直接匹配：要素类型完全一致 → 权重 1.0
   - 层次匹配：要素类型存在上下级关系 → 权重 0.8
   - 相关匹配：要素类型语义相关 → 权重 0.6
   - 功能匹配：要素功能相似 → 权重 0.4

3. 时间维度匹配（权重：20%）
   - 完全覆盖：数据时间范围完全覆盖需求 → 权重 1.0
   - 部分覆盖：数据时间范围部分覆盖需求 → 权重 0.8
   - 时间邻近：数据时间接近但不重叠 → 权重 0.6
   - 时间相关：数据时间与需求相关 → 权重 0.4

4. 质量维度匹配（权重：10%）
   - 高质量：数据质量评级为优秀 → 权重 1.0
   - 中等质量：数据质量评级为良好 → 权重 0.8
   - 基本质量：数据质量评级为合格 → 权重 0.6
   - 低质量：数据质量评级为待改进 → 权重 0.4

综合评分计算：
总分 = 地理匹配分 × 0.3 + 语义匹配分 × 0.4 + 时间匹配分 × 0.2 + 质量匹配分 × 0.1

阈值设定：
- 高置信度：总分 ≥ 0.8
- 中等置信度：0.6 ≤ 总分 < 0.8
- 低置信度：0.4 ≤ 总分 < 0.6
- 不推荐：总分 < 0.4