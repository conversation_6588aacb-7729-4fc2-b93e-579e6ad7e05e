基于检索框架，执行具体的数据检索任务：

检索任务信息：
- 检索目标：{search_objective}
- 概念实体：{concept_entities}
- 约束条件：{constraints}
- 质量要求：{quality_requirements}

执行步骤：

步骤1：概念实体解析
对检索目标进行深度解析：
- 地理实体解析：{geographic_entities}
  * 如果是"中东地区" → 解析为具体国家列表
  * 如果是"沿海城市" → 解析为海岸线附近的城市
  * 如果是"边境地区" → 解析为国界附近的区域
- 要素实体解析：{feature_entities}
  * 如果是"港口" → 映射到港口数据表的查询条件
  * 如果是"机场" → 映射到机场数据表的查询条件
  * 如果是"基础设施" → 映射到多个相关数据表
- 时间实体解析：{temporal_entities}
  * 如果是"近期" → 转换为具体的时间范围
  * 如果是"历史" → 确定历史数据的时间边界
  * 如果是"实时" → 确定数据更新频率要求

步骤2：数据集发现
在数据集索引表中搜索相关数据集：
- 构建搜索查询：基于解析后的实体构建搜索条件
- 执行多维匹配：
  * 地理范围匹配：数据集覆盖范围与目标区域的重叠度
  * 主题领域匹配：数据集主题与要素类型的相关性
  * 时间范围匹配：数据集时效与需求时间的符合度
  * 质量等级匹配：数据集质量与要求标准的符合度
- 候选集排序：基于综合匹配分数排序候选数据集
- 结果验证：验证候选数据集的可用性和完整性

步骤3：数据表定位
在选定数据集中定位具体数据表：
- 字段匹配分析：
  * 必需字段检查：验证数据表是否包含必需的字段
  * 字段类型验证：确认字段类型与需求的兼容性
  * 字段完整性评估：评估关键字段的数据完整性
- 语义相似度计算：
  * 利用向量化语义字段进行相似度计算
  * 基于数据表描述进行语义匹配
  * 考虑字段名称和描述的语义相关性
- 数据质量评估：
  * 数据新鲜度：最后更新时间与当前时间的差距
  * 数据完整性：缺失值的比例和分布
  * 数据准确性：基于历史使用情况的准确性评估

步骤4：记录精确提取
从确定的数据表中提取符合条件的记录：
- 构建查询条件：
  * 空间条件：基于地理边界构建空间查询
  * 时间条件：基于时间范围构建时间过滤
  * 属性条件：基于要素特征构建属性过滤
  * 关联条件：基于实体关系构建关联查询
- 执行查询优化：
  * 查询计划优化：选择最优的查询执行计划
  * 索引利用：充分利用数据表的索引结构
  * 分页处理：对大结果集进行分页处理
- 结果后处理：
  * 数据清洗：处理异常值和缺失值
  * 格式标准化：统一数据格式和编码
  * 质量标记：为每条记录标记质量等级

输出要求：
提供详细的检索执行报告，包含每个步骤的执行结果、遇到的问题、采用的解决方案和最终的数据质量评估。